### 10. GET方法URL长度限制的原因



实际上HTTP协议规范并没有对get方法请求的url长度进行限制，这个限制是特定的浏览器及服务器对它的限制。



IE对URL长度的限制是2083字节(2K+35)。由于IE浏览器对URL长度的允许值是最小的，所以开发过程中，只要URL不超过2083字节，那么在所有浏览器中工作都不会有问题。



```plain
GET的长度值 = URL（2083）- （你的Domain+Path）-2（2是get请求中?=两个字符的长度）
```



下面看一下主流浏览器对get方法中url的长度限制范围：



+ Microsoft Internet Explorer (Browser)：IE浏览器对URL的最大限制为2083个字符，如果超过这个数字，提交按钮没有任何反应。
+ Firefox (Browser)：对于Firefox浏览器URL的长度限制为 65,536 个字符。
+ Safari (Browser)：URL最大长度限制为 80,000 个字符。
+ Opera (Browser)：URL最大长度限制为 190,000 个字符。
+ Google (chrome)：URL最大长度限制为 8182 个字符。



主流的服务器对get方法中url的长度限制范围：



+ Apache (Server)：能接受最大url长度为8192个字符。
+ Microsoft Internet Information Server(IIS)：能接受最大url的长度为16384个字符。



根据上面的数据，可以知道，get方法中的URL长度最长不超过2083个字符，这样所有的浏览器和服务器都可能正常工作。



