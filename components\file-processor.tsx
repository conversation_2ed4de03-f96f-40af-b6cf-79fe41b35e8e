"use client";

import { useState, useEffect } from "react";
import { FileTree, FileNode } from "./file-tree";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Play, Loader2 } from "lucide-react";
import ReactMarkdown from "react-markdown";

export function FileProcessor() {
  const [inputFiles, setInputFiles] = useState<FileNode[]>([]);
  const [outputFiles, setOutputFiles] = useState<FileNode[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [previewFile, setPreviewFile] = useState<{ name: string; content: string } | null>(null);

  // 加载文件树
  useEffect(() => {
    loadFileTrees();
  }, []);

  const loadFileTrees = async () => {
    try {
      const [inputRes, outputRes] = await Promise.all([
        fetch("/api/files/input"),
        fetch("/api/files/output"),
      ]);

      if (inputRes.ok) {
        const inputData = await inputRes.json();
        setInputFiles(inputData.files || []);
      }

      if (outputRes.ok) {
        const outputData = await outputRes.json();
        setOutputFiles(outputData.files || []);
      }
    } catch (error) {
      console.error("加载文件树失败:", error);
    }
  };

  const handleProcess = async () => {
    if (selectedFiles.size === 0) {
      alert("请至少选择一个文件");
      return;
    }

    setIsProcessing(true);
    setProgress(0);

    try {
      const response = await fetch("/api/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          files: Array.from(selectedFiles),
          rule: "revise", // 默认使用 revise 规则
        }),
      });

      if (!response.ok) {
        throw new Error("处理失败");
      }

      // 使用 Server-Sent Events 接收进度更新
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split("\n");

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === "progress") {
                setProgress((data.completed / data.total) * 100);
              } else if (data.type === "complete") {
                setProgress(100);
                // 重新加载输出文件树
                await loadFileTrees();
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("处理失败:", error);
      alert("处理失败，请查看控制台");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFilePreview = async (file: FileNode) => {
    try {
      const response = await fetch(`/api/files/content?path=${encodeURIComponent(file.path)}`);
      if (response.ok) {
        const data = await response.json();
        setPreviewFile({
          name: file.name,
          content: data.content,
        });
      }
    } catch (error) {
      console.error("加载文件内容失败:", error);
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Input 文件树 */}
      <Card>
        <CardHeader>
          <CardTitle>输入文件</CardTitle>
          <CardDescription>选择要处理的文件</CardDescription>
        </CardHeader>
        <CardContent>
          <FileTree
            nodes={inputFiles}
            selectedFiles={selectedFiles}
            onSelectionChange={setSelectedFiles}
            mode="input"
          />
          <div className="mt-4 flex items-center justify-between">
            <span className="text-sm text-muted-foreground">
              已选择 {selectedFiles.size} 个文件
            </span>
            <Button
              onClick={handleProcess}
              disabled={isProcessing || selectedFiles.size === 0}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  开始处理
                </>
              )}
            </Button>
          </div>
          {isProcessing && (
            <div className="mt-4">
              <Progress value={progress} />
              <p className="text-sm text-muted-foreground mt-2 text-center">
                {Math.round(progress)}%
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output 文件树 */}
      <Card>
        <CardHeader>
          <CardTitle>输出文件</CardTitle>
          <CardDescription>点击已完成的文件预览内容</CardDescription>
        </CardHeader>
        <CardContent>
          <FileTree
            nodes={outputFiles}
            selectedFiles={new Set()}
            onFileClick={handleFilePreview}
            mode="output"
          />
        </CardContent>
      </Card>

      {/* 文件预览对话框 */}
      <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{previewFile?.name}</DialogTitle>
          </DialogHeader>
          <div className="prose dark:prose-invert max-w-none">
            <ReactMarkdown>{previewFile?.content || ""}</ReactMarkdown>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

