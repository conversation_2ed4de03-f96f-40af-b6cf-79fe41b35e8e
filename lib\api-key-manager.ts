/**
 * API Key 管理器
 * 负责智能分配 API keys，实现负载均衡和速率限制
 */

export interface ApiKeyConfig {
  key: string;
  name: string;
}

interface KeyUsage {
  key: string;
  name: string;
  requestCount: number;
  lastUsed: number;
  isAvailable: boolean;
  errorCount: number;
}

export class ApiKeyManager {
  private keys: KeyUsage[] = [];
  private readonly maxRequestsPerMinute = 15; // Gemini API 限制
  private readonly cooldownPeriod = 60000; // 1分钟冷却期
  private readonly maxErrors = 3; // 最大错误次数

  constructor(apiKeys: ApiKeyConfig[]) {
    this.keys = apiKeys.map(config => ({
      key: config.key,
      name: config.name,
      requestCount: 0,
      lastUsed: 0,
      isAvailable: true,
      errorCount: 0,
    }));
  }

  /**
   * 获取下一个可用的 API key
   * 使用轮询策略，优先选择使用次数最少的 key
   */
  getNextKey(): string | null {
    const now = Date.now();
    
    // 重置超过冷却期的 key
    this.keys.forEach(keyUsage => {
      if (now - keyUsage.lastUsed > this.cooldownPeriod) {
        keyUsage.requestCount = 0;
        if (keyUsage.errorCount < this.maxErrors) {
          keyUsage.isAvailable = true;
        }
      }
    });

    // 找到可用的 key（未达到速率限制且可用）
    const availableKeys = this.keys.filter(
      k => k.isAvailable && k.requestCount < this.maxRequestsPerMinute
    );

    if (availableKeys.length === 0) {
      console.warn('所有 API keys 都不可用或已达到速率限制');
      return null;
    }

    // 选择使用次数最少的 key
    const selectedKey = availableKeys.reduce((prev, current) =>
      prev.requestCount < current.requestCount ? prev : current
    );

    // 更新使用统计
    selectedKey.requestCount++;
    selectedKey.lastUsed = now;

    console.log(`使用 API Key: ${selectedKey.name} (使用次数: ${selectedKey.requestCount}/${this.maxRequestsPerMinute})`);
    
    return selectedKey.key;
  }

  /**
   * 报告 API key 使用成功
   */
  reportSuccess(key: string) {
    const keyUsage = this.keys.find(k => k.key === key);
    if (keyUsage) {
      keyUsage.errorCount = 0; // 重置错误计数
    }
  }

  /**
   * 报告 API key 使用失败
   */
  reportError(key: string) {
    const keyUsage = this.keys.find(k => k.key === key);
    if (keyUsage) {
      keyUsage.errorCount++;
      console.warn(`API Key ${keyUsage.name} 错误次数: ${keyUsage.errorCount}/${this.maxErrors}`);
      
      if (keyUsage.errorCount >= this.maxErrors) {
        keyUsage.isAvailable = false;
        console.error(`API Key ${keyUsage.name} 已被禁用（错误次数过多）`);
      }
    }
  }

  /**
   * 获取所有 keys 的状态
   */
  getStatus() {
    return this.keys.map(k => ({
      name: k.name,
      requestCount: k.requestCount,
      isAvailable: k.isAvailable,
      errorCount: k.errorCount,
    }));
  }

  /**
   * 重置所有 keys 的状态
   */
  reset() {
    this.keys.forEach(k => {
      k.requestCount = 0;
      k.errorCount = 0;
      k.isAvailable = true;
      k.lastUsed = 0;
    });
  }
}

// 单例实例
let apiKeyManagerInstance: ApiKeyManager | null = null;

export function getApiKeyManager(): ApiKeyManager {
  if (!apiKeyManagerInstance) {
    // 从环境变量加载 API keys
    const apiKeys: ApiKeyConfig[] = [
      { key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw', name: 'My First Project' },
      { key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y', name: 'ankibot' },
      { key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY', name: 'Generative Language Client' },
      { key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc', name: 'In The Novel' },
      { key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk', name: 'chat' },
    ];
    
    apiKeyManagerInstance = new ApiKeyManager(apiKeys);
  }
  
  return apiKeyManagerInstance;
}

