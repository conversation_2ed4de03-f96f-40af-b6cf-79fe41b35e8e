import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({});

async function main(systemInstruction) {
    const response = await ai.models.generateContent({
        model: "gemini-2.5-pro",
        contents: "Explain how AI works in a few words",
        config: {
            thinkingConfig: {
                thinkingBudget: 32000, // Disables thinking
            },
            systemInstruction
        }
    });
    console.log(response.text);
}

await main();