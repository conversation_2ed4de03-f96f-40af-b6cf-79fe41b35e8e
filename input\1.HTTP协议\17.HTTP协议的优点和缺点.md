### 17. HTTP协议的优点和缺点



HTTP 是超文本传输协议，它定义了客户端和服务器之间交换报文的格式和方式，默认使用 80 端口。它使用 TCP 作为传输层协议，保证了数据传输的可靠性。



HTTP协议具有以下**优点**：



+ 支持客户端/服务器模式
+ **简单快速**：客户向服务器请求服务时，只需传送请求方法和路径。由于 HTTP 协议简单，使得 HTTP 服务器的程序规模小，因而通信速度很快。
+ **无连接**：无连接就是限制每次连接只处理一个请求。服务器处理完客户的请求，并收到客户的应答后，即断开连接，采用这种方式可以节省传输时间。
+ **无状态**：HTTP 协议是无状态协议，这里的状态是指通信过程的上下文信息。缺少状态意味着如果后续处理需要前面的信息，则它必须重传，这样可能会导致每次连接传送的数据量增大。另一方面，在服务器不需要先前信息时它的应答就比较快。
+ **灵活**：HTTP 允许传输任意类型的数据对象。正在传输的类型由 Content-Type 加以标记。



HTTP协议具有以下**缺点**：



+ **无状态：**HTTP 是一个无状态的协议，HTTP 服务器不会保存关于客户的任何信息。
+ **明文传输：**协议中的报文使用的是文本形式，这就直接暴露给外界，不安全。
+ **不安全**



（1）通信使用明文（不加密），内容可能会被窃听；



（2）不验证通信方的身份，因此有可能遭遇伪装；



（3）无法证明报文的完整性，所以有可能已遭篡改；



