"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { FileProcessor } from "@/components/file-processor";
import { Studio } from "@/components/studio";

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto p-6">
        <header className="mb-8">
          <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100 mb-2">
            Learning Studio
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            使用 AI 转换和增强你的学习材料
          </p>
        </header>

        <Tabs defaultValue="processor" className="w-full">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="processor">文件处理</TabsTrigger>
            <TabsTrigger value="studio">工作室</TabsTrigger>
          </TabsList>
          
          <TabsContent value="processor" className="mt-6">
            <FileProcessor />
          </TabsContent>
          
          <TabsContent value="studio" className="mt-6">
            <Studio />
          </TabsContent>
        </Tabs>
      </div>
    </main>
  );
}

