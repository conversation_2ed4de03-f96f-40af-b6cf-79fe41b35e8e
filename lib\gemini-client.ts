/**
 * Gemini API 客户端
 * 封装 Gemini API 调用，支持并发处理和错误重试
 */

import { GoogleGenerativeAI } from "@google/generative-ai";
import { getApiKeyManager } from "./api-key-manager";

export interface GeminiConfig {
  model: string;
  config: {
    thinkingConfig?: {
      thinkingBudget?: number;
    };
    tools?: any[];
    systemInstruction: string;
  };
}

export interface ProcessResult {
  success: boolean;
  content?: string;
  error?: string;
  fileName: string;
}

/**
 * 使用 Gemini API 处理单个文件
 */
export async function processWithGemini(
  content: string,
  ruleConfig: GeminiConfig,
  fileName: string,
  maxRetries = 3
): Promise<ProcessResult> {
  const keyManager = getApiKeyManager();
  let lastError: Error | null = null;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    const apiKey = keyManager.getNextKey();
    
    if (!apiKey) {
      // 等待一段时间后重试
      if (attempt < maxRetries - 1) {
        console.log(`等待 API key 可用... (尝试 ${attempt + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 5000));
        continue;
      }
      return {
        success: false,
        error: '所有 API keys 都不可用',
        fileName,
      };
    }

    try {
      const genAI = new GoogleGenerativeAI(apiKey);
      const model = genAI.getGenerativeModel({ 
        model: ruleConfig.model,
        systemInstruction: ruleConfig.config.systemInstruction,
      });

      const result = await model.generateContent({
        contents: [{ role: "user", parts: [{ text: content }] }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        },
      });

      const response = result.response;
      const text = response.text();

      keyManager.reportSuccess(apiKey);

      return {
        success: true,
        content: text,
        fileName,
      };
    } catch (error: any) {
      lastError = error;
      keyManager.reportError(apiKey);
      
      console.error(`处理文件 ${fileName} 失败 (尝试 ${attempt + 1}/${maxRetries}):`, error.message);

      // 如果是速率限制错误，等待后重试
      if (error.message?.includes('429') || error.message?.includes('quota')) {
        if (attempt < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000 * (attempt + 1)));
          continue;
        }
      }

      // 其他错误，立即重试下一个 key
      if (attempt < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        continue;
      }
    }
  }

  return {
    success: false,
    error: lastError?.message || '未知错误',
    fileName,
  };
}

/**
 * 并发处理多个文件
 */
export async function processBatch(
  files: Array<{ path: string; content: string }>,
  ruleConfig: GeminiConfig,
  onProgress?: (completed: number, total: number, result: ProcessResult) => void,
  maxConcurrent = 3
): Promise<ProcessResult[]> {
  const results: ProcessResult[] = [];
  const queue = [...files];
  let completed = 0;

  // 创建并发处理池
  const workers = Array(Math.min(maxConcurrent, files.length))
    .fill(null)
    .map(async () => {
      while (queue.length > 0) {
        const file = queue.shift();
        if (!file) break;

        const result = await processWithGemini(
          file.content,
          ruleConfig,
          file.path
        );

        results.push(result);
        completed++;

        if (onProgress) {
          onProgress(completed, files.length, result);
        }
      }
    });

  await Promise.all(workers);

  return results;
}

/**
 * 加载规则配置
 */
export async function loadRuleConfig(ruleName: string): Promise<GeminiConfig | null> {
  try {
    const response = await fetch(`/api/rules/${ruleName}`);
    if (!response.ok) {
      throw new Error(`加载规则失败: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`加载规则 ${ruleName} 失败:`, error);
    return null;
  }
}

/**
 * 保存规则配置
 */
export async function saveRuleConfig(ruleName: string, config: GeminiConfig): Promise<boolean> {
  try {
    const response = await fetch(`/api/rules/${ruleName}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    });
    return response.ok;
  } catch (error) {
    console.error(`保存规则 ${ruleName} 失败:`, error);
    return false;
  }
}

