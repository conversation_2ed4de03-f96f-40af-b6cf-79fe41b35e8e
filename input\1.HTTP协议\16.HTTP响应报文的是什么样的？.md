### 16. HTTP响应报文的是什么样的？



请求报⽂有4部分组成:



+ 响应⾏
+ 响应头
+ 空⾏
+ 响应体



![](https://cdn.nlark.com/yuque/0/2021/png/1500604/1615907658281-b3a51c98-db27-45be-9b0c-fb2c7665b015.png?x-oss-process=image%2Fwatermark%2Ctype_d3F5LW1pY3JvaGVp%2Csize_39%2Ctext_5pyI5ZOl55qE6Z2i6K-V6K6t57uD6JCl%2Ccolor_FFFFFF%2Cshadow_50%2Ct_80%2Cg_se%2Cx_10%2Cy_10)



+ 响应⾏：由网络协议版本，状态码和状态码的原因短语组成，例如 HTTP/1.1 200 OK 。
+ 响应头：响应部⾸组成
+ 响应体：服务器响应的数据



