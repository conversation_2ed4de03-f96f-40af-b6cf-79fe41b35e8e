### 14. HTTP2的头部压缩算法是怎样的？



HTTP2的头部压缩是HPACK算法。在客户端和服务器两端建立“字典”，用索引号表示重复的字符串，采用哈夫曼编码来压缩整数和字符串，可以达到50%~90%的高压缩率。



具体来说:



+ 在客户端和服务器端使用“首部表”来跟踪和存储之前发送的键值对，对于相同的数据，不再通过每次请求和响应发送；
+ 首部表在HTTP/2的连接存续期内始终存在，由客户端和服务器共同渐进地更新；
+ 每个新的首部键值对要么被追加到当前表的末尾，要么替换表中之前的值。



例如下图中的两个请求， 请求一发送了所有的头部字段，第二个请求则只需要发送差异数据，这样可以减少冗余数据，降低开销。



![](https://cdn.nlark.com/yuque/0/2020/png/1500604/1604070508591-32d79893-7e98-40c8-b779-ffb6da42cd1b.png?x-oss-process=image%2Fwatermark%2Ctype_d3F5LW1pY3JvaGVp%2Csize_14%2Ctext_5pyI5ZOl55qE6Z2i6K-V6K6t57uD6JCl%2Ccolor_FFFFFF%2Cshadow_50%2Ct_80%2Cg_se%2Cx_10%2Cy_10)



