import { NextRequest, NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ name: string }> }
) {
  try {
    const { name } = await params;
    const rulePath = path.join(process.cwd(), "rules", `${name}.js`);

    try {
      const content = await fs.readFile(rulePath, "utf-8");
      
      // 简单的 JS 解析：提取导出的配置对象
      // 这是一个简化版本，实际项目中可能需要更复杂的解析
      const match = content.match(/export\s+const\s+\w+Config\s*=\s*({[\s\S]*?});?\s*$/m);
      
      if (match) {
        // 使用 eval 解析配置（注意：这在生产环境中不安全，仅用于演示）
        // 更好的方法是使用 JSON 格式存储规则
        const configStr = match[1];
        const config = eval(`(${configStr})`);
        return NextResponse.json(config);
      }

      return NextResponse.json(
        { error: "Invalid rule format" },
        { status: 400 }
      );
    } catch (error) {
      // 如果文件不存在，返回默认配置
      return NextResponse.json({
        model: "gemini-2.0-flash-exp",
        config: {
          systemInstruction: `你是一个专业的内容生成助手。`,
        }
      });
    }
  } catch (error) {
    console.error("获取规则失败:", error);
    return NextResponse.json(
      { error: "Failed to get rule" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ name: string }> }
) {
  try {
    const { name } = await params;
    const config = await request.json();

    const rulePath = path.join(process.cwd(), "rules", `${name}.js`);

    // 将配置转换为 JS 文件格式
    const content = `export const ${name}Config = ${JSON.stringify(config, null, 2)};
`;

    await fs.writeFile(rulePath, content, "utf-8");

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("保存规则失败:", error);
    return NextResponse.json(
      { error: "Failed to save rule" },
      { status: 500 }
    );
  }
}

