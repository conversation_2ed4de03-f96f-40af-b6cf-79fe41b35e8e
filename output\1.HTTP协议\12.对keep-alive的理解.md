### 12. 对keep-alive的理解



HTTP1.0 中默认是在每次请求/应答，客户端和服务器都要新建一个连接，完成之后立即断开连接，这就是**短连接**。当使用Keep-Alive模式时，Keep-Alive功能使客户端到服务器端的连接持续有效，当出现对服务器的后继请求时，Keep-Alive功能避免了建立或者重新建立连接，这就是**长连接**。其使用方法如下：



+ HTTP1.0版本是默认没有Keep-alive的（也就是默认会发送keep-alive），所以要想连接得到保持，必须手动配置发送`Connection: keep-alive`字段。若想断开keep-alive连接，需发送`Connection:close`字段；
+ HTTP1.1规定了默认保持长连接，数据传输完成了保持TCP连接不断开，等待在同域名下继续用这个通道传输数据。如果需要关闭，需要客户端发送`Connection：close`首部字段。



Keep-Alive的**建立过程**：



+ 客户端向服务器在发送请求报文同时在首部添加发送Connection字段
+ 服务器收到请求并处理 Connection字段
+ 服务器回送Connection:Keep-Alive字段给客户端
+ 客户端接收到Connection字段
+ Keep-Alive连接建立成功



**服务端自动断开过程（也就是没有keep-alive）**：



+ 客户端向服务器只是发送内容报文（不包含Connection字段）
+ 服务器收到请求并处理
+ 服务器返回客户端请求的资源并关闭连接
+ 客户端接收资源，发现没有Connection字段，断开连接



**客户端请求断开连接过程**：



+ 客户端向服务器发送Connection:close字段
+ 服务器收到请求并处理connection字段
+ 服务器回送响应资源并断开连接
+ 客户端接收资源并断开连接



开启Keep-Alive的**优点：**



+ 较少的CPU和内存的使⽤（由于同时打开的连接的减少了）；
+ 允许请求和应答的HTTP管线化；
+ 降低拥塞控制 （TCP连接减少了）；
+ 减少了后续请求的延迟（⽆需再进⾏握⼿）；
+ 报告错误⽆需关闭TCP连；



开启Keep-Alive的**缺点**：



+ 长时间的Tcp连接容易导致系统资源无效占用，浪费系统资源。



