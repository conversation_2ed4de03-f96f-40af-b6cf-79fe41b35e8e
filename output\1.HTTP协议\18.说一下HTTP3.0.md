### 18. 说一下HTTP 3.0![](https://cdn.nlark.com/yuque/0/2020/webp/1500604/1604068246276-9b0f553d-3c6e-43a3-8185-8565f9fa1fb4.webp?x-oss-process=image%2Fwatermark%2Ctype_d3F5LW1pY3JvaGVp%2Csize_33%2Ctext_5pyI5ZOl55qE6Z2i6K-V6K6t57uD6JCl%2Ccolor_FFFFFF%2Cshadow_50%2Ct_80%2Cg_se%2Cx_10%2Cy_10)



HTTP/3基于UDP协议实现了类似于TCP的多路复用数据流、传输可靠性等功能，这套功能被称为QUIC协议。







1. 流量控制、传输可靠性功能：QUIC在UDP的基础上增加了一层来保证数据传输可靠性，它提供了数据包重传、拥塞控制、以及其他一些TCP中的特性。
2. 集成TLS加密功能：目前QUIC使用TLS1.3，减少了握手所花费的RTT数。
3. 多路复用：同一物理连接上可以有多个独立的逻辑数据流，实现了数据流的单独传输，解决了TCP的队头阻塞问题。



![](https://cdn.nlark.com/yuque/0/2020/webp/1500604/1604068246276-5d0a5de2-00db-425e-8b21-0cc4bbb54b24.webp?x-oss-process=image%2Fwatermark%2Ctype_d3F5LW1pY3JvaGVp%2Csize_33%2Ctext_5pyI5ZOl55qE6Z2i6K-V6K6t57uD6JCl%2Ccolor_FFFFFF%2Cshadow_50%2Ct_80%2Cg_se%2Cx_10%2Cy_10)



1. 快速握手：由于基于UDP，可以实现使用0 ~ 1个RTT来建立连接。



