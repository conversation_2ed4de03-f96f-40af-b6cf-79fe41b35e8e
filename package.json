{"name": "learning-studio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "if exist .next rmdir /s /q .next"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "next": "15.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^9.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.2"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.5.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}