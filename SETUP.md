# 项目设置指南

## 快速开始

### 方法 1: 使用 npm (推荐)

```bash
# 1. 安装依赖（已完成）
npm install

# 2. 启动开发服务器
npm run dev
```

### 方法 2: 如果遇到权限问题

如果在 Windows 上遇到 `.next/trace` 权限错误，请尝试以下方法：

#### 选项 A: 以管理员身份运行

1. 右键点击 PowerShell 或 CMD
2. 选择"以管理员身份运行"
3. 导航到项目目录
4. 运行 `npm run dev`

#### 选项 B: 清理并重启

```powershell
# 删除 .next 文件夹
Remove-Item -Recurse -Force .next -ErrorAction SilentlyContinue

# 启动开发服务器
npm run dev
```

#### 选项 C: 使用提供的脚本

```powershell
# 使用 PowerShell 脚本
.\start.ps1
```

### 方法 3: 手动启动

如果自动化脚本不工作，请手动执行以下步骤：

1. 打开新的终端窗口
2. 确保没有其他 Next.js 进程在运行
3. 删除 `.next` 文件夹（如果存在）
4. 运行 `npm run dev`

## 访问应用

启动成功后，在浏览器中访问：
- http://localhost:3000 (如果端口可用)
- http://localhost:3001 (如果 3000 被占用)

## 项目结构

```
learning-studio/
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   │   ├── files/        # 文件系统 API
│   │   ├── process/      # 处理 API
│   │   └── rules/        # 规则管理 API
│   ├── globals.css       # 全局样式
│   ├── layout.tsx        # 根布局
│   └── page.tsx          # 主页面
├── components/            # React 组件
│   ├── ui/               # UI 基础组件
│   ├── file-tree.tsx     # 文件树组件
│   ├── file-processor.tsx # 文件处理器
│   └── studio.tsx        # 工作室组件
├── lib/                   # 工具库
│   ├── api-key-manager.ts # API Key 管理器
│   ├── gemini-client.ts   # Gemini 客户端
│   └── utils.ts          # 工具函数
├── input/                 # 输入文件夹
├── output/                # 输出文件夹
└── rules/                 # 规则配置
```

## 功能说明

### 1. 文件处理

- 在"文件处理"标签页
- 左侧显示 input 文件夹的文件树
- 勾选要处理的文件
- 点击"开始处理"
- 右侧会显示处理结果

### 2. Studio 工作室

- 在"工作室"标签页
- 选择一个模板（Audio Overview, Video Overview 等）
- 点击编辑按钮修改规则
- 保存后可在文件处理中使用

### 3. API Key 管理

系统会自动管理多个 Gemini API keys：
- 智能负载均衡
- 速率限制保护
- 自动错误重试
- 并发处理支持

## 常见问题

### Q: 端口被占用怎么办？

A: Next.js 会自动使用下一个可用端口（如 3001）。

### Q: 如何添加新的规则？

A: 
1. 在 Studio 中创建新模板
2. 或直接在 `rules/` 文件夹中创建新的 `.js` 文件
3. 格式参考 `rules/revise.js`

### Q: 如何修改 API Keys？

A: 编辑 `lib/api-key-manager.ts` 文件中的 `apiKeys` 数组。

### Q: 处理失败怎么办？

A: 
1. 检查控制台错误信息
2. 确认 API keys 有效
3. 检查网络连接
4. 查看 API 配额是否用完

## 下一步

1. 将你的学习材料（Markdown 文件）放入 `input/` 文件夹
2. 在 Studio 中配置你需要的规则
3. 开始处理文件
4. 在 `output/` 文件夹中查看结果

## 技术支持

如果遇到问题：
1. 查看浏览器控制台
2. 查看终端输出
3. 检查 API key 配置
4. 确认文件路径正确

祝使用愉快！🎉

