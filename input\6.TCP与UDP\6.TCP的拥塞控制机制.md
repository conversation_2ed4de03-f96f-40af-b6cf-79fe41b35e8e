### 6. TCP的拥塞控制机制



TCP的拥塞控制机制主要是以下四种机制：



+ 慢启动（慢开始）
+ 拥塞避免
+ 快速重传
+ 快速恢复



**（1）慢启动（慢开始）**



+  在开始发送的时候设置cwnd = 1（cwnd指的是拥塞窗口） 
+  思路：开始的时候不要发送大量数据，而是先测试一下网络的拥塞程度，由小到大增加拥塞窗口的大小。 
+  为了防止cwnd增长过大引起网络拥塞，设置一个慢开始门限(ssthresh 状态变量) 
+  
    - 当cnwd < ssthresh，使用慢开始算法
    - 当cnwd = ssthresh，既可使用慢开始算法，也可以使用拥塞避免算法
    - 当cnwd > ssthresh，使用拥塞避免算法



**（2）拥塞避免**



+  拥塞避免未必能够完全避免拥塞，是说在拥塞避免阶段将拥塞窗口控制为按线性增长，使网络不容易出现阻塞。 
+  思路： 让拥塞窗口cwnd缓慢的增大，即每经过一个返回时间RTT就把发送方的拥塞控制窗口加一 
+  无论是在慢开始阶段还是在拥塞避免阶段，只要发送方判断网络出现拥塞，就把慢开始门限设置为出现拥塞时的发送窗口大小的一半。然后把拥塞窗口设置为1，执行慢开始算法。如图所示:  
![](https://cdn.nlark.com/yuque/0/2020/png/1500604/1604022952123-62276cba-d882-46c9-8a1c-70655dc501af.png?x-oss-process=image%2Fwatermark%2Ctype_d3F5LW1pY3JvaGVp%2Csize_32%2Ctext_5pyI5ZOl55qE6Z2i6K-V6K6t57uD6JCl%2Ccolor_FFFFFF%2Cshadow_50%2Ct_80%2Cg_se%2Cx_10%2Cy_10)  
+  其中，判断网络出现拥塞的根据就是没有收到确认，虽然没有收到确认可能是其他原因的分组丢失，但是因为无法判定，所以都当做拥塞来处理。 



**（3）快速重传**



+ 快重传要求接收方在收到一个失序的报文段后就立即发出重复确认(为的是使发送方及早知道有报文段没有到达对方)。发送方只要连续收到三个重复确认就立即重传对方尚未收到的报文段，而不必继续等待设置的重传计时器时间到期。
+ 由于不需要等待设置的重传计时器到期，能尽早重传未被确认的报文段，能提高整个网络的吞吐量



**（4）快速恢复 **



+ 当发送方连续收到三个重复确认时，就执行“乘法减小”算法，![](https://cdn.nlark.com/yuque/0/2020/png/1500604/1604022952153-a7106d22-225d-4081-9b0a-56b0d1876bc2.png?x-oss-process=image%2Fwatermark%2Ctype_d3F5LW1pY3JvaGVp%2Csize_41%2Ctext_5pyI5ZOl55qE6Z2i6K-V6K6t57uD6JCl%2Ccolor_FFFFFF%2Cshadow_50%2Ct_80%2Cg_se%2Cx_10%2Cy_10)把ssthresh门限减半。但是接下去并不执行慢开始算法。
+ 考虑到如果网络出现拥塞的话就不会收到好几个重复的确认，所以发送方现在认为网络可能没有出现拥塞。所以此时不执行慢开始算法，而是将cwnd设置为ssthresh的大小，然后执行拥塞避免算法。  




