### 3. 4XX (Client Error 客户端错误状态码)



4XX 的响应结果表明客户端是发生错误的原因所在。



#### （1）400 Bad Request


该状态码表示请求报文中存在语法错误。当错误发生时，需修改请求的内容后再次发送请求。另外，浏览器会像 200 OK 一样对待该状态码。



#### （2）401 Unauthorized


该状态码表示发送的请求需要有通过 HTTP 认证(BASIC 认证、DIGEST 认证)的认证信息。若之前已进行过一次请求，则表示用户认证失败



返回含有 401 的响应必须包含一个适用于被请求资源的 WWW-Authenticate 首部用以质询(challenge)用户信息。当浏览器初次接收到 401 响应，会弹出认证用的对话窗口。



以下情况会出现401：



+ 401.1 - 登录失败。
+ 401.2 - 服务器配置导致登录失败。
+ 401.3 - 由于 ACL 对资源的限制而未获得授权。
+ 401.4 - 筛选器授权失败。
+ 401.5 - ISAPI/CGI 应用程序授权失败。
+ 401.7 - 访问被 Web 服务器上的 URL 授权策略拒绝。这个错误代码为 IIS 6.0 所专用。



#### （3）403 Forbidden


该状态码表明请求资源的访问被服务器拒绝了，服务器端没有必要给出详细理由，但是可以在响应报文实体的主体中进行说明。进入该状态后，不能再继续进行验证。该访问是永久禁止的，并且与应用逻辑密切相关。



IIS 定义了许多不同的 403 错误，它们指明更为具体的错误原因：



+ 403.1 - 执行访问被禁止。
+ 403.2 - 读访问被禁止。
+ 403.3 - 写访问被禁止。
+ 403.4 - 要求 SSL。
+ 403.5 - 要求 SSL 128。
+ 403.6 - IP 地址被拒绝。
+ 403.7 - 要求客户端证书。
+ 403.8 - 站点访问被拒绝。
+ 403.9 - 用户数过多。
+ 403.10 - 配置无效。
+ 403.11 - 密码更改。
+ 403.12 - 拒绝访问映射表。
+ 403.13 - 客户端证书被吊销。
+ 403.14 - 拒绝目录列表。
+ 403.15 - 超出客户端访问许可。
+ 403.16 - 客户端证书不受信任或无效。
+ 403.17 - 客户端证书已过期或尚未生效
+ 403.18 - 在当前的应用程序池中不能执行所请求的 URL。这个错误代码为 IIS 6.0 所专用。
+ 403.19 - 不能为这个应用程序池中的客户端执行 CGI。这个错误代码为 IIS 6.0 所专用。
+ 403.20 - Passport 登录失败。这个错误代码为 IIS 6.0 所专用。



#### （4）404 Not Found


该状态码表明服务器上无法找到请求的资源。除此之外，也可以在服务器端拒绝请求且不想说明理由时使用。



以下情况会出现404：



+ 404.0 -（无） – 没有找到文件或目录。
+ 404.1 - 无法在所请求的端口上访问 Web 站点。
+ 404.2 - Web 服务扩展锁定策略阻止本请求。
+ 404.3 - MIME 映射策略阻止本请求。



#### （5）405 Method Not Allowed


该状态码表示客户端请求的方法虽然能被服务器识别，但是服务器禁止使用该方法。GET 和 HEAD 方法，服务器应该总是允许客户端进行访问。客户端可以通过 OPTIONS 方法（预检）来查看服务器允许的访问方法, 如下



```plain
Access-Control-Allow-Methods: GET,HEAD,PUT,PATCH,POST,DELETE
```



