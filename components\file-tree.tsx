"use client";

import { useState } from "react";
import { ChevronRight, ChevronDown, File, Folder, Check, X } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

export interface FileNode {
  name: string;
  path: string;
  type: "file" | "directory";
  children?: FileNode[];
  status?: "pending" | "processing" | "completed" | "error";
}

interface FileTreeProps {
  nodes: FileNode[];
  selectedFiles: Set<string>;
  onSelectionChange?: (selected: Set<string>) => void;
  onFileClick?: (file: FileNode) => void;
  mode?: "input" | "output";
}

export function FileTree({
  nodes,
  selectedFiles,
  onSelectionChange,
  onFileClick,
  mode = "input",
}: FileTreeProps) {
  return (
    <ScrollArea className="h-[600px] w-full rounded-md border p-4">
      <div className="space-y-1">
        {nodes.map((node) => (
          <TreeNode
            key={node.path}
            node={node}
            selectedFiles={selectedFiles}
            onSelectionChange={onSelectionChange}
            onFileClick={onFileClick}
            mode={mode}
            level={0}
          />
        ))}
      </div>
    </ScrollArea>
  );
}

interface TreeNodeProps {
  node: FileNode;
  selectedFiles: Set<string>;
  onSelectionChange?: (selected: Set<string>) => void;
  onFileClick?: (file: FileNode) => void;
  mode: "input" | "output";
  level: number;
}

function TreeNode({
  node,
  selectedFiles,
  onSelectionChange,
  onFileClick,
  mode,
  level,
}: TreeNodeProps) {
  const [isExpanded, setIsExpanded] = useState(level < 2); // 默认展开前两层
  const isSelected = selectedFiles.has(node.path);
  const isDirectory = node.type === "directory";

  const handleCheckboxChange = (checked: boolean) => {
    if (!onSelectionChange) return;

    const newSelected = new Set(selectedFiles);
    
    if (checked) {
      // 选中当前节点及所有子节点
      addNodeAndChildren(node, newSelected);
    } else {
      // 取消选中当前节点及所有子节点
      removeNodeAndChildren(node, newSelected);
    }

    onSelectionChange(newSelected);
  };

  const addNodeAndChildren = (n: FileNode, set: Set<string>) => {
    if (n.type === "file") {
      set.add(n.path);
    }
    if (n.children) {
      n.children.forEach((child) => addNodeAndChildren(child, set));
    }
  };

  const removeNodeAndChildren = (n: FileNode, set: Set<string>) => {
    set.delete(n.path);
    if (n.children) {
      n.children.forEach((child) => removeNodeAndChildren(child, set));
    }
  };

  const getStatusIcon = () => {
    if (mode === "input" || !node.status) return null;

    switch (node.status) {
      case "completed":
        return <Check className="h-4 w-4 text-green-500" />;
      case "error":
        return <X className="h-4 w-4 text-red-500" />;
      case "processing":
        return (
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        );
      default:
        return null;
    }
  };

  const getNodeClassName = () => {
    if (mode === "output") {
      if (node.status === "pending") {
        return "text-muted-foreground opacity-50";
      }
      if (node.status === "completed") {
        return "text-foreground cursor-pointer hover:text-primary";
      }
      if (node.status === "error") {
        return "text-destructive";
      }
    }
    return "text-foreground";
  };

  const handleNodeClick = () => {
    if (mode === "output" && node.type === "file" && node.status === "completed") {
      onFileClick?.(node);
    }
  };

  return (
    <div>
      <div
        className={cn(
          "flex items-center gap-2 rounded-md px-2 py-1.5 hover:bg-accent",
          getNodeClassName()
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
      >
        {isDirectory && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-0 hover:bg-transparent"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </button>
        )}

        {mode === "input" && node.type === "file" && (
          <Checkbox
            checked={isSelected}
            onCheckedChange={handleCheckboxChange}
          />
        )}

        {mode === "output" && getStatusIcon()}

        <div
          className="flex items-center gap-2 flex-1"
          onClick={handleNodeClick}
        >
          {isDirectory ? (
            <Folder className="h-4 w-4 text-blue-500" />
          ) : (
            <File className="h-4 w-4 text-slate-500" />
          )}
          <span className="text-sm">{node.name}</span>
        </div>
      </div>

      {isDirectory && isExpanded && node.children && (
        <div>
          {node.children.map((child) => (
            <TreeNode
              key={child.path}
              node={child}
              selectedFiles={selectedFiles}
              onSelectionChange={onSelectionChange}
              onFileClick={onFileClick}
              mode={mode}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}

