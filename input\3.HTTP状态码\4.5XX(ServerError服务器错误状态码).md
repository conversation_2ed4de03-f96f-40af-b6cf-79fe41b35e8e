### 4. 5XX (Server Error 服务器错误状态码)



5XX 的响应结果表明服务器本身发生错误.



#### （1）500 Internal Server Error


该状态码表明服务器端在执行请求时发生了错误。也有可能是 Web 应用存在的 bug 或某些临时的故障。



#### （2）502 Bad Gateway


该状态码表明扮演网关或代理角色的服务器，从上游服务器中接收到的响应是无效的。注意，502 错误通常不是客户端能够修复的，而是需要由途经的 Web 服务器或者代理服务器对其进行修复。以下情况会出现502：



+ 502.1 - CGI （通用网关接口）应用程序超时。
+ 502.2 - CGI （通用网关接口）应用程序出错。



#### （3）503 Service Unavailable


该状态码表明服务器暂时处于超负载或正在进行停机维护，现在无法处理请求。如果事先得知解除以上状况需要的时间，最好写入 RetryAfter 首部字段再返回给客户端。



**使用场景：**



+ 服务器停机维护时，主动用503响应请求；
+ nginx 设置限速，超过限速，会返回503。



#### （4）504 Gateway Timeout


该状态码表示网关或者代理的服务器无法在规定的时间内获得想要的响应。他是HTTP 1.1中新加入的。



使用场景：代码执行时间超时，或者发生了死循环。



